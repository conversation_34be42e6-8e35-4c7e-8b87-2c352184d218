import os, json, asyncio, logging
from datetime import datetime
from dotenv import load_dotenv
from concurrent.futures import ThreadPoolExecutor
import discord

from scrapers.site_scrapers import search_all_sites, filter_offers
from utils.notify import make_deal_embed

load_dotenv()
DISCORD_TOKEN = os.getenv("DISCORD_TOKEN")
CHANNEL_ID = int(os.getenv("DISCORD_CHANNEL_ID"))
INTERVAL = int(os.getenv("CHECK_INTERVAL_MINUTES", "15"))
PRICE_LIMIT = float(os.getenv("PRICE_LIMIT_EUR", "50"))
MIN_DROP = float(os.getenv("MIN_DROP_PERCENT", "5"))

STATE_FILE = "state.json"
QUERIES_FILE = "queries.json"
DIRECT_TARGETS_FILE = "direct_targets.json"

logging.basicConfig(level=os.getenv("LOG_LEVEL","INFO"))
log = logging.getLogger("pokemon-deals-bot")


intents = discord.Intents.default()
client = discord.Client(intents=intents)
executor = ThreadPoolExecutor(max_workers=8)

def load_json(path, default):
    try:
        with open(path, "r", encoding="utf-8") as f:
            return json.load(f)
    except Exception:
        return default

def save_json(path, data):
    with open(path, "w", encoding="utf-8") as f:
        json.dump(data, f, indent=2, ensure_ascii=False)

def load_state():
    return load_json(STATE_FILE, {})

def save_state(state):
    save_json(STATE_FILE, state)

def compute_total(offer):
    return (offer.get("price") or 0) + (offer.get("shipping") or 0)

def get_best_offer(offers):
    offers = [o for o in offers if o.get("price") is not None]
    if not offers:
        return None
    offers.sort(key=lambda o: compute_total(o))
    return offers[0]

def run_search_for_query(query):
    # search across sites with keywords, then filter
    offers = search_all_sites(query["keywords"])
    offers = filter_offers(offers, price_limit=PRICE_LIMIT)
    return offers

async def monitor_loop():
    await client.wait_until_ready()
    channel = client.get_channel(CHANNEL_ID)
    if channel is None:
        log.error("Channel introuvable: %s", CHANNEL_ID)
        return
    state = load_state()
    loop = asyncio.get_event_loop()

    while not client.is_closed():
        queries = load_json(QUERIES_FILE, [])
        # Direct targets could be implemented in future; currently everything is search-based
        for q in queries:
            sku = q["sku"]
            offers = await loop.run_in_executor(executor, run_search_for_query, q)
            best = get_best_offer(offers)
            if not best:
                log.info("Aucune offre pour %s", sku)
                continue
            prev = state.get(sku)
            should_notify = False
            if not prev:
                should_notify = True
            else:
                prev_total = (prev.get("price") or 0) + (prev.get("shipping") or 0)
                new_total = compute_total(best)
                if new_total < prev_total:
                    drop_pct = (prev_total - new_total) / max(prev_total, 0.01) * 100
                    if drop_pct >= MIN_DROP:
                        should_notify = True
            if should_notify:
                embed = make_deal_embed(q.get("label", sku), sku, best, previous=prev)
                try:
                    await channel.send(embed=embed)
                    log.info("Notif envoyée pour %s : %.2f €", sku, compute_total(best))
                except Exception as e:
                    log.exception("Erreur envoi Discord: %s", e)
                state[sku] = {"price": best.get("price"), "shipping": best.get("shipping"), "url": best.get("url"), "title": best.get("title"), "site": best.get("site"), "ts": datetime.utcnow().isoformat()}
                save_state(state)
        await asyncio.sleep(INTERVAL * 60)

@client.event
async def on_ready():
    log.info("Connecté en tant que %s", client.user)
    client.loop.create_task(monitor_loop())

if __name__ == "__main__":
    client.run(DISCORD_TOKEN)
