# Pokémon Deals Bot (Botasaurus + Discord)

Bot complet pour surveiller des **offres Pokémon** (Flamme Blanche / Foudre Noire, ETB, Tripack, Coffrets, Boosters, Displays) sur plusieurs sites FR, avec filtres :
- Pays FR (ou vendeur FR si marketplace)
- État *Neuf* ou *Near Mint*
- Prix total (prix + frais) ≤ **50 €** (configurable via `.env`)
- Notifications vers Discord (embeds) lorsque de nouvelles **meilleures offres** sont trouvées ou qu'une offre baisse suffisamment.

## Stack
- **Botasaurus** : scraping anti-bot (requests + option navigateur), cache, user-agent rotation.
- **discord.py** : envoi des notifications.
- **BeautifulSoup** + heuristiques pour extraire prix, état, frais de port.
- **ThreadPool** : paralléliser les scrapes sans bloquer l'event loop Discord.

## Installation
```bash
python -m pip install -r requirements.txt
cp .env.example .env  # remplissez les valeurs
python main.py
```

### Docker
```bash
docker-compose up --build
```

## Configuration (.env)
- `DISCORD_TOKEN` : token du bot Discord
- `DISCORD_CHANNEL_ID` : ID du channel où poster
- `CHECK_INTERVAL_MINUTES` : intervalle de scan
- `MIN_DROP_PERCENT` : seuil de baisse pour renvoyer une notif
- `SHIP_TO_COUNTRY` / `SHIP_TO_CITY` : utilisés pour filtrer les vendeurs FR
- `PRICE_LIMIT_EUR` : plafond (par défaut 50 €)

## Produits suivis
Le fichier `queries.json` contient des **requêtes** par extension/type plutôt que des URLs fixes, ce qui évite les liens obsolètes.  
Chaque scraper site construit une URL de recherche et filtre les résultats.

Vous pouvez aussi ajouter des URL directes dans `direct_targets.json` si vous souhaitez forcer le suivi de pages précises.

## Test rapide
```bash
python test_scrapers.py
```
Affiche dans la console les meilleures offres trouvées immédiatement (sans Discord).

## Notes
- Les sites peuvent changer (HTML, anti-bot). Les scrapers incluent des fallback et une option headless navigateur à activer si besoin (voir `scrapers/base.py`).  
- Les frais de port sont estimés à partir des textes visibles. Certains vendeurs ne donnent le prix exact qu'au panier : dans ce cas, nous supposons **0 €** ou **livraison gratuite** si détectée, sinon nous ignorons l'offre si le prix est trop incertain.
