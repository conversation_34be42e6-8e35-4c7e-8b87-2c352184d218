from datetime import datetime
import discord
from discord import Embed

def make_deal_embed(product_label, sku, offer, previous=None):
    e = Embed(
        title=f"Meilleure offre — {product_label}",
        url=offer["url"],
        description=offer.get("title","").strip()[:200],
        timestamp=datetime.utcnow()
    )
    e.add_field(name="Prix", value=f"{offer['price']:.2f} €", inline=True)
    ship = offer.get("shipping")
    if ship is not None:
        e.add_field(name="Frais de port", value=f"{ship:.2f} €", inline=True)
        e.add_field(name="Total", value=f"{(offer['price']+ship):.2f} €", inline=True)
    seller = offer.get("seller") or offer.get("site")
    if seller:
        e.add_field(name="Vendeur/Site", value=str(seller)[:100], inline=False)
    if previous:
        prev_total = previous.get("price",0)+(previous.get("shipping") or 0)
        new_total = offer["price"] + (offer.get("shipping") or 0)
        if prev_total > 0 and new_total < prev_total:
            delta = prev_total - new_total
            pct = (delta / prev_total) * 100
            e.add_field(name="Baisse", value=f"-{pct:.1f}% ({delta:.2f} €)", inline=False)
    e.set_footer(text=f"SKU: {sku}")
    return e
