import json
from scrapers.site_scrapers import search_all_sites, filter_offers

queries = json.load(open("queries.json","r",encoding="utf-8"))

for q in queries:
    print("\n==>", q["label"], "==>")
    offers = search_all_sites(q["keywords'])
    filtered = filter_offers(offers, price_limit=50.0)
    for o in filtered[:10]:
        total = (o.get("price") or 0) + (o.get("shipping") or 0)
        print(f"- {o.get('site')}: {o.get('title')[:90]} | {o.get('price')} € + {o.get('shipping')} € = {total:.2f} -> {o.get('url')}")
