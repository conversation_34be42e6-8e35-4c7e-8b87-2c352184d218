import re
from urllib.parse import quote_plus, urljoin
from bs4 import BeautifulSoup
from .base import get_soup, text_price_to_float, find_shipping_estimate

PRICE_LIMIT_DEFAULT = 50.0

# ---------- Generic helpers ----------
def qualify_condition(text):
    t = (text or "").lower()
    if any(k in t for k in ["neuf", "near mint", "nm", "sealed", "scellé", "scelle"]):
        return "Neuf/NM"
    return None

def looks_french(text):
    t = (text or "").lower()
    return any(k in t for k in ["france", "fr", "expédié de france", "vendeur français", "expedie de france", "livré depuis france"])

def build_offer(title, price, url, site, shipping=None, seller=None, condition=None):
    return {
        "title": (title or "").strip(),
        "price": price,
        "shipping": shipping,
        "url": url,
        "site": site,
        "seller": seller,
        "condition": condition or ""
    }

# ---------- FNAC ----------
def search_fnac(query):
    q = quote_plus(query)
    url = f"https://www.fnac.com/SearchResult/ResultList.aspx?SCat=2%211&Search={q}"
    soup = get_soup(url)
    offers = []
    for item in soup.select("article.Article-item, div.article-item")[:50]:
        title = item.get_text(" ", strip=True)
        link = item.select_one("a")
        href = urljoin(url, link['href']) if link and link.get('href') else url
        price_str = None
        prc = item.select_one(".f-price, .userPrice, [data-price]")
        if prc:
            price_str = prc.get_text(" ", strip=True) or prc.get("data-price")
        price = text_price_to_float(price_str)
        shipping = None
        condition = qualify_condition(title)
        if price is not None:
            offers.append(build_offer(title, price, href, "fnac", shipping=shipping, seller="Fnac", condition=condition))
    return offers

# ---------- CDISCOUNT ----------
def search_cdiscount(query):
    q = quote_plus(query)
    url = f"https://www.cdiscount.com/search/10/{q}.html"
    soup = get_soup(url)
    offers = []
    for item in soup.select(".jsPrdtB, .prdtBloc")[:50]:
        title_el = item.select_one(".prdtBILst .prdtBTit, .prdtTit")
        title = title_el.get_text(" ", strip=True) if title_el else item.get_text(" ", strip=True)[:140]
        link = item.select_one("a")
        href = urljoin(url, link['href']) if link and link.get('href') else url
        prc = item.select_one(".price, .fpPrice, .prdtPrSt")
        price = text_price_to_float(prc.get_text(" ", strip=True) if prc else None)
        shipping = None
        condition = qualify_condition(title)
        if price is not None:
            offers.append(build_offer(title, price, href, "cdiscount", shipping=shipping, seller="Cdiscount", condition=condition))
    return offers

# ---------- AMAZON FR ----------
def search_amazon_fr(query):
    q = quote_plus(query)
    url = f"https://www.amazon.fr/s?k={q}"
    soup = get_soup(url)
    offers = []
    for item in soup.select("div.s-result-item")[:50]:
        title_el = item.select_one("h2 a span")
        if not title_el:
            continue
        title = title_el.get_text(" ", strip=True)
        href_el = item.select_one("h2 a")
        href = urljoin(url, href_el['href']) if href_el and href_el.get('href') else url
        price_whole = item.select_one("span.a-price-whole")
        price_frac = item.select_one("span.a-price-fraction")
        if not price_whole:
            continue
        price_str = (price_whole.get_text("").replace('.', '') + "." + (price_frac.get_text("") if price_frac else "00"))
        price = text_price_to_float(price_str)
        shipping = None
        ship_text = item.get_text(" ", strip=True).lower()
        if "livraison gratuite" in ship_text:
            shipping = 0.0
        condition = qualify_condition(title)
        if price is not None:
            offers.append(build_offer(title, price, href, "amazon", shipping=shipping, seller="Amazon", condition=condition))
    return offers

# ---------- CARDMARKET ----------
def search_cardmarket(query):
    q = quote_plus(query)
    url = f"https://www.cardmarket.com/fr/Pokemon/Products/Search?searchString={q}"
    soup = get_soup(url)
    offers = []
    for row in soup.select(".table > tbody > tr"):
        title_el = row.select_one(".product-title")
        if not title_el:
            continue
        title = title_el.get_text(" ", strip=True)
        href_el = row.select_one("a")
        href = urljoin(url, href_el['href']) if href_el and href_el.get('href') else url
        price_el = row.select_one(".text-right span")
        price = text_price_to_float(price_el.get_text(" ", strip=True) if price_el else None)
        # Prefer FR sellers is better at offer page level; here we accept as generic
        if price is not None:
            offers.append(build_offer(title, price, href, "cardmarket", shipping=None, seller=None, condition=qualify_condition(title)))
    return offers

# ---------- LEBONCOIN ----------
def search_leboncoin(query):
    q = quote_plus(query)
    # Utiliser une URL sans restriction de localisation, se concentrer sur le prix uniquement
    url = f"https://www.leboncoin.fr/recherche?text={q}&price=min-50"
    soup = get_soup(url)
    offers = []
    for item in soup.select("a[href*='/annonce/']")[:50]:
        title = item.get_text(" ", strip=True)
        href = urljoin(url, item.get('href'))
        # price appears often near the card
        parent = item.parent
        price = None
        if parent:
            t = parent.get_text(" ", strip=True)
            price = text_price_to_float(t)
        if price is not None:
            offers.append(build_offer(title, price, href, "leboncoin", shipping=None, seller="Particulier/Pro", condition=qualify_condition(title)))
    return offers

# ---------- Router ----------
SITE_SEARCHERS = {
    "fnac": search_fnac,
    "cdiscount": search_cdiscount,
    "amazon": search_amazon_fr,
    "cardmarket": search_cardmarket,
    "leboncoin": search_leboncoin,
}

def search_all_sites(keywords):
    query = " ".join(keywords)
    all_offers = []
    for site, fn in SITE_SEARCHERS.items():
        try:
            site_offers = fn(query)
            for o in site_offers:
                o["site"] = site
            all_offers.extend(site_offers)
        except Exception as e:
            # swallow and continue
            pass
    return all_offers

def filter_offers(offers, price_limit=PRICE_LIMIT_DEFAULT):
    out = []
    for o in offers:
        total = (o.get("price") or 0) + (o.get("shipping") or 0)
        if total == 0:
            continue
        # must be Neuf/NM
        if not qualify_condition(o.get("title")) and not qualify_condition(o.get("condition")):
            continue
        # Supprimer la vérification de localisation française - se concentrer uniquement sur le prix
        # Accepter toutes les offres des sites de confiance
        if total <= price_limit:
            out.append(o)
    # dedupe by title
    seen = set()
    dedup = []
    for o in sorted(out, key=lambda x: (x.get("price", 1e9) + (x.get("shipping") or 0))):
        key = o.get("title").lower()[:120]
        if key in seen:
            continue
        seen.add(key)
        dedup.append(o)
    return dedup
