import logging
from bs4 import BeautifulSoup

log = logging.getLogger(__name__)

# Import Botasaurus create_request with fallbacks for API differences
try:
    from botasaurus.request import create_request
except Exception:  # pragma: no cover
    try:
        from botasaurus import bt
        def create_request(*args, **kwargs):
            return bt.create_request(*args, **kwargs)
    except Exception:
        raise ImportError("Botasaurus non trouvé. Installez-le via pip.")

def get_soup(url, timeout=40, **kwargs):
    """Return BeautifulSoup for the given URL using Botasaurus anti-detect requests."""
    req = create_request()
    try:
        # Use req.get() to get the response, then create BeautifulSoup object
        resp = req.get(url, timeout=timeout, **kwargs)
        resp.raise_for_status()  # Raise an exception for bad status codes
        return BeautifulSoup(resp.text, "lxml")
    except Exception as e:
        log.warning("req.get failed (%s). Retrying with basic approach", e)
        # Fallback: try again with basic approach
        try:
            resp = req.get(url, timeout=timeout, **kwargs)
            return BeautifulSoup(resp.text, "lxml")
        except Exception as e2:
            log.error("Both attempts failed. Last error: %s", e2)
            # Return empty soup as last resort
            return BeautifulSoup("<html></html>", "lxml")

def text_price_to_float(text):
    """Extract a float price from arbitrary text like '29,99 €'"""
    if not text:
        return None
    import re
    m = re.search(r"([0-9]+[\s\.,]?[0-9]{0,2})", text.replace('\xa0',' ').strip())
    if not m:
        return None
    s = m.group(1).replace(' ', '').replace(',', '.')
    try:
        return float(s)
    except Exception:
        return None

def find_shipping_estimate(soup):
    """Best-effort: look for shipping hints like 'Livraison gratuite', 'À partir de X €'"""
    texts = soup.get_text(" \n ", strip=True).lower()
    if "livraison gratuite" in texts or "expédition gratuite" in texts:
        return 0.0
    import re
    m = re.search(r"livraison\s+(?:à partir de\s+)?([0-9]+[\.,]?[0-9]{0,2})\s*€", texts)
    if m:
        try:
            return float(m.group(1).replace(',', '.'))
        except Exception:
            return None
    return None
